package ui.layout.left.display.components.container.picture;

import common.constant.ResourceConstant;
import lombok.extern.slf4j.Slf4j;
import sdk.base.operation.Operation;
import sdk.base.operation.OperationMethod;
import sdk.base.operation.OperationResult;
import sdk.domain.Device;
import sdk.entity.AndroidDevice;
import ui.base.picture.ADBPanel;
import ui.base.picture.AndroidPictureRectDrawLabel;
import ui.base.picture.PictureRectDrawLabel;
import ui.base.picture.ScaledPoint;
import ui.entry.ClientView;
import ui.model.MainModel;
import ui.model.app.AppObserver;
import ui.utils.SwingUtil;

import javax.imageio.ImageIO;
import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.Collections;
import java.util.List;

@Slf4j
public class AndroidPictureContainer extends PictureContainer implements AppObserver {
    private JLabel screenShotButton;
    private SwingWorker<Void, Void> screenshotWorker;
    private ADBPanel adbPanel;

    // 视频流相关字段
    private InputStream videoInputStream;
    private SwingWorker<Boolean, BufferedImage> videoStreamWorker;
    private volatile boolean isStreamRunning = false;

    public AndroidPictureContainer(ClientView clientView, MainModel mainModel, Device device) {
        super(clientView, mainModel, device);
    }

    public AndroidPictureContainer(ClientView clientView, MainModel mainModel, Device device, boolean alwaysDynamic) {
        super(clientView, mainModel, device, alwaysDynamic);
    }


    @Override
    public void createView() {
        screenShotButton = new JLabel(SwingUtil.getResourceAsImageIcon(ResourceConstant.LeftLayout.takePhotoIconPath));
        super.createView();
        setPlayOrPauseButtonVisible(false);
        JTabbedPane tabbedPane = new JTabbedPane();
        JPanel panel = new JPanel(new BorderLayout());
        panel.add(getPicturePanel(), BorderLayout.CENTER);
        panel.add(getToolBox(), BorderLayout.SOUTH);
        tabbedPane.addTab("Android", panel);
//        tabbedPane.addTab("发送ADB命令与接收", new AdbSendPanel(this, getMainModel()));
        tabbedPane.addTab("ADB命令面板", new ADBPanel(getMainModel(), getDevice()));
        setLayout(new BorderLayout());
        add(tabbedPane, BorderLayout.CENTER);

    }

    @Override
    protected PictureRectDrawLabel getPictureRectDrawLabel() {
        return new AndroidPictureRectDrawLabel(getMainModel(), this);
    }

    @Override
    protected List<JLabel> toolButtonList() {
        return Collections.singletonList(screenShotButton);
    }

    @Override
    public void registerModelObservers() {
        getMainModel().getAppModel().registerObserver(this);
    }

    private void screenShot() {
        initScreenshotTasks().execute();
    }

    private SwingWorker<Void, Void> initScreenshotTasks() {
        return new SwingWorker<Void, Void>() {
            @Override
            protected Void doInBackground() {
                screenShotButton.setEnabled(false);
                screenShotButton.repaint();
                AndroidDevice androidDevice = (AndroidDevice) getDevice();
                OperationResult operationResult = androidDevice.screenshot();
                if (operationResult.isOk()) {
                    String filePath = (String) operationResult.getData();
                    try {
                        BufferedImage bufferedImage = ImageIO.read(new File(filePath));
                        getPicturePanel().setImageStream(bufferedImage);
                    } catch (IOException e) {
                        SwingUtil.showWarningDialog(AndroidPictureContainer.this, e.getMessage());
                    }
                } else {
                    SwingUtil.showWarningDialog(AndroidPictureContainer.this, operationResult.getMessage());
                }
                return null;
            }

            @Override
            protected void done() {
                screenShotButton.setEnabled(true);
                screenShotButton.repaint();
            }
        };
    }

    @Override
    public void createActions() {
        super.createActions();
        screenShotButton.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                if (screenShotButton.isEnabled()) {
                    screenShot();
                }
            }
        });
    }

    @Override
    public boolean pictureOperationStart(OperationMethod operationMethod) {
        return true;
    }

    @Override
    public boolean pictureOperating(Operation operation) {
        return true;
    }

    @Override
    public void pictureDoubleClick(ScaledPoint point) {
    }

    public void grab() {
        AndroidDevice device = (AndroidDevice) getDevice();
        String deviceName = device.getDeviceName();

        // 如果已经有视频流在运行，先停止
        if (isStreamRunning) {
            stopVideoStream();
        }

        log.info("开始启动Android设备视频流: {}", deviceName);

        videoStreamWorker = new SwingWorker<Boolean, BufferedImage>() {
            @Override
            protected Boolean doInBackground() throws Exception {
                try {
                    startGrab();
                    publish((BufferedImage) null); // 清空显示
                    getPicturePanel().setText("正在连接Android设备视频流: " + deviceName);

                    // 调用设备的videoStream方法，这会直接调用后端的/android/screencap接口
                    videoInputStream = device.videoStream();
                    if (videoInputStream == null) {
                        getPicturePanel().setText("无法启动视频流: 设备返回null");
                        return false;
                    }

                    isStreamRunning = true;
                    getPicturePanel().clearText();
                    completeGrab();

                    // 优化的视频流读取逻辑
                    java.io.BufferedInputStream bufferedStream = new java.io.BufferedInputStream(videoInputStream, 64 * 1024); // 64KB缓冲
                    java.io.ByteArrayOutputStream frameBuffer = new java.io.ByteArrayOutputStream();
                    byte[] buffer = new byte[32 * 1024]; // 32KB读取缓冲区
                    long lastFrameTime = System.currentTimeMillis();
                    int frameCount = 0;

                    while (isStreamRunning && !isCancelled()) {
                        try {
                            int bytesRead = bufferedStream.read(buffer);
                            if (bytesRead == -1) {
                                log.info("视频流结束: {}", deviceName);
                                break;
                            }

                            if (bytesRead > 0) {
                                frameBuffer.write(buffer, 0, bytesRead);

                                // 尝试解析累积的数据为图像帧
                                byte[] frameData = frameBuffer.toByteArray();
                                if (frameData.length > 1024) { // 至少1KB数据才尝试解析
                                    try (java.io.ByteArrayInputStream imageStream = new java.io.ByteArrayInputStream(frameData)) {
                                        BufferedImage frame = ImageIO.read(imageStream);
                                        if (frame != null) {
                                            publish(frame);
                                            frameBuffer.reset(); // 清空缓冲区，准备下一帧
                                            frameCount++;

                                            // 每秒统计一次帧率
                                            long currentTime = System.currentTimeMillis();
                                            if (currentTime - lastFrameTime >= 1000) {
                                                log.debug("视频流帧率: {} fps, 设备: {}", frameCount, deviceName);
                                                frameCount = 0;
                                                lastFrameTime = currentTime;
                                            }
                                        }
                                    } catch (Exception e) {
                                        // 数据可能不完整，继续累积
                                        if (frameBuffer.size() > 1024 * 1024) { // 如果缓冲区超过1MB，重置
                                            log.debug("帧缓冲区过大，重置: {}", deviceName);
                                            frameBuffer.reset();
                                        }
                                    }
                                }
                            }

                            // 避免CPU占用过高
                            if (frameBuffer.size() == 0) {
                                Thread.sleep(1); // 短暂休眠
                            }

                        } catch (java.io.IOException e) {
                            if (isStreamRunning) {
                                log.error("读取视频流数据失败: {}, 错误: {}", deviceName, e.getMessage());
                                break;
                            }
                        } catch (InterruptedException e) {
                            Thread.currentThread().interrupt();
                            break;
                        }
                    }

                    return true;
                } catch (Exception e) {
                    log.error("Android视频流获取失败: {}, 错误: {}", deviceName, e.getMessage(), e);
                    getPicturePanel().setText("视频流获取失败: " + e.getMessage());
                    return false;
                }
            }

            @Override
            protected void process(List<BufferedImage> chunks) {
                if (!chunks.isEmpty() && getPicturePanel() != null) {
                    BufferedImage latestFrame = chunks.get(chunks.size() - 1);
                    if (latestFrame != null) {
                        getPicturePanel().setImageStream(latestFrame);
                    }
                }
            }

            @Override
            protected void done() {
                try {
                    Boolean result = get();
                    if (!result) {
                        log.warn("Android视频流获取任务完成，但结果为失败");
                    }
                } catch (Exception e) {
                    log.error("Android视频流获取任务异常", e);
                } finally {
                    stopVideoStream();
                }
            }
        };

        videoStreamWorker.execute();
    }

    /**
     * 停止视频流
     */
    private void stopVideoStream() {
        isStreamRunning = false;

        // 关闭视频流输入流
        if (videoInputStream != null) {
            try {
                videoInputStream.close();
                log.info("Android视频流输入流已关闭: {}", getDevice().getDeviceName());
            } catch (Exception e) {
                log.warn("关闭Android视频流输入流失败: {}, 错误: {}", getDevice().getDeviceName(), e.getMessage());
            } finally {
                videoInputStream = null;
            }
        }

        // 取消工作线程
        if (videoStreamWorker != null && !videoStreamWorker.isDone()) {
            videoStreamWorker.cancel(true);
        }

        log.info("Android视频流已停止: {}", getDevice().getDeviceName());
    }

    @Override
    public void appExit() {
        stopVideoStream();
    }

}
