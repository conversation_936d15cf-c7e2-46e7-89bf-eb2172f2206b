package ui.layout.left.display.components.container.picture;

import common.constant.ResourceConstant;
import lombok.extern.slf4j.Slf4j;
import sdk.base.operation.Operation;
import sdk.base.operation.OperationMethod;
import sdk.base.operation.OperationResult;
import sdk.base.VideoStreamHandler;
import sdk.domain.Device;
import sdk.entity.AdbHudDevice;
import sdk.utils.VideoStreamClient;
import ui.base.picture.*;
import ui.entry.ClientView;
import ui.model.MainModel;
import ui.model.app.AppObserver;
import ui.utils.SwingUtil;

import javax.imageio.ImageIO;
import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.util.Collections;
import java.util.List;

@Slf4j
public class AdbHudPictureContainer extends PictureContainer implements AppObserver {
    private JLabel screenShotButton;
    private SwingWorker<Void, Void> screenshotWorker;

    // 视频流相关字段
    private VideoStreamHandler videoStreamHandler;
    private SwingWorker<Boolean, BufferedImage> videoStreamWorker;
    private volatile boolean isStreamRunning = false;
    private ADBPanel adbPanel;

    public AdbHudPictureContainer(ClientView clientView, MainModel mainModel, Device device) {
        super(clientView, mainModel, device);
    }

    public AdbHudPictureContainer(ClientView clientView, MainModel mainModel, Device device, boolean alwaysDynamic) {
        super(clientView, mainModel, device, alwaysDynamic);
    }


    @Override
    public void createView() {
        screenShotButton = new JLabel(SwingUtil.getResourceAsImageIcon(ResourceConstant.LeftLayout.takePhotoIconPath));
        super.createView();
        setPlayOrPauseButtonVisible(false);
        JTabbedPane tabbedPane = new JTabbedPane();
        JPanel panel = new JPanel(new BorderLayout());
        panel.add(getPicturePanel(), BorderLayout.CENTER);
        panel.add(getToolBox(), BorderLayout.SOUTH);
        AdbSendPanel AdbSendPanel = new AdbSendPanel(this, getMainModel());
        tabbedPane.addTab("AdbHud", panel);
        tabbedPane.addTab("发送ADB指令", AdbSendPanel);
        setLayout(new BorderLayout());
        add(tabbedPane, BorderLayout.CENTER);

    }

    @Override
    protected PictureRectDrawLabel getPictureRectDrawLabel() {
        return new AndroidPictureRectDrawLabel(getMainModel(), this);
    }

    @Override
    protected List<JLabel> toolButtonList() {
        return Collections.singletonList(screenShotButton);
    }

    @Override
    public void registerModelObservers() {
        getMainModel().getAppModel().registerObserver(this);
    }

    private void screenShot() {
        initScreenshotTasks().execute();
    }

    private SwingWorker<Void, Void> initScreenshotTasks() {
        return new SwingWorker<Void, Void>() {
            @Override
            protected Void doInBackground() {
                screenShotButton.setEnabled(false);
                screenShotButton.repaint();
                AdbHudDevice adbHudDevice = (AdbHudDevice) getDevice();
                OperationResult operationResult = adbHudDevice.screenshot();
                if (operationResult.isOk()) {
                    String filePath = (String) operationResult.getData();
                    try {
                        BufferedImage bufferedImage = ImageIO.read(new File(filePath));
                        getPicturePanel().setImageStream(bufferedImage);
                    } catch (IOException e) {
                        SwingUtil.showWarningDialog(AdbHudPictureContainer.this, e.getMessage());
                    }
                } else {
                    SwingUtil.showWarningDialog(AdbHudPictureContainer.this, operationResult.getMessage());
                }
                return null;
            }

            @Override
            protected void done() {
                screenShotButton.setEnabled(true);
                screenShotButton.repaint();
            }
        };
    }

    @Override
    public void createActions() {
        super.createActions();
        screenShotButton.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                if (screenShotButton.isEnabled()) {
                    screenShot();
                }
            }
        });
    }

    @Override
    public boolean pictureOperationStart(OperationMethod operationMethod) {
        return true;
    }

    @Override
    public boolean pictureOperating(Operation operation) {
        return true;
    }

    @Override
    public void pictureDoubleClick(ScaledPoint point) {
    }

    public void grab() {
        AdbHudDevice device = (AdbHudDevice) getDevice();
        //TODO:为了降低耦合，后续可以考虑放到控制器中管理
        SwingWorker<Boolean, String> worker = new SwingWorker<Boolean, String>() {

            @Override
            protected Boolean doInBackground() throws Exception {
                String deviceName = device.getDeviceName();
                log.info("打开ADB HUD设备:{}", deviceName);
                if (!device.isConnected()) {
                    OperationResult response = device.openDevice();
                    if (response.isOk()) {
                        startGrab();
                        return true;
                    } else {
                        publish(response.getMessage());
                        completeGrab();
                        return false;
                    }
                } else {
                    startGrab();
                    return true;
                }
            }

            @Override
            protected void process(List<String> chunks) {
                for (String chunk : chunks) {
                    getPicturePanel().setText(chunk);
                }
            }
        };
        worker.execute();
    }

}
