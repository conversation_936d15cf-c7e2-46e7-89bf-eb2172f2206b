package ui.layout.left.display.components.container.picture;

import common.constant.ResourceConstant;
import lombok.extern.slf4j.Slf4j;
import sdk.base.operation.Operation;
import sdk.base.operation.OperationMethod;
import sdk.base.operation.OperationResult;
import sdk.base.VideoStreamHandler;
import sdk.domain.Device;
import sdk.entity.AdbHudDevice;
import sdk.utils.VideoStreamClient;
import ui.base.picture.*;
import ui.entry.ClientView;
import ui.model.MainModel;
import ui.model.app.AppObserver;
import ui.utils.SwingUtil;

import javax.imageio.ImageIO;
import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.util.Collections;
import java.util.List;

@Slf4j
public class AdbHudPictureContainer extends PictureContainer implements AppObserver {
    private JLabel screenShotButton;
    private SwingWorker<Void, Void> screenshotWorker;

    // 视频流相关字段
    private VideoStreamHandler videoStreamHandler;
    private SwingWorker<Boolean, BufferedImage> videoStreamWorker;
    private volatile boolean isStreamRunning = false;
    private ADBPanel adbPanel;

    public AdbHudPictureContainer(ClientView clientView, MainModel mainModel, Device device) {
        super(clientView, mainModel, device);
    }

    public AdbHudPictureContainer(ClientView clientView, MainModel mainModel, Device device, boolean alwaysDynamic) {
        super(clientView, mainModel, device, alwaysDynamic);
    }


    @Override
    public void createView() {
        screenShotButton = new JLabel(SwingUtil.getResourceAsImageIcon(ResourceConstant.LeftLayout.takePhotoIconPath));
        super.createView();
        setPlayOrPauseButtonVisible(false);
        JTabbedPane tabbedPane = new JTabbedPane();
        JPanel panel = new JPanel(new BorderLayout());
        panel.add(getPicturePanel(), BorderLayout.CENTER);
        panel.add(getToolBox(), BorderLayout.SOUTH);
        AdbSendPanel AdbSendPanel = new AdbSendPanel(this, getMainModel());
        tabbedPane.addTab("AdbHud", panel);
        tabbedPane.addTab("发送ADB指令", AdbSendPanel);
        setLayout(new BorderLayout());
        add(tabbedPane, BorderLayout.CENTER);

    }

    @Override
    protected PictureRectDrawLabel getPictureRectDrawLabel() {
        return new AndroidPictureRectDrawLabel(getMainModel(), this);
    }

    @Override
    protected List<JLabel> toolButtonList() {
        return Collections.singletonList(screenShotButton);
    }

    @Override
    public void registerModelObservers() {
        getMainModel().getAppModel().registerObserver(this);
    }

    private void screenShot() {
        initScreenshotTasks().execute();
    }

    private SwingWorker<Void, Void> initScreenshotTasks() {
        return new SwingWorker<Void, Void>() {
            @Override
            protected Void doInBackground() {
                screenShotButton.setEnabled(false);
                screenShotButton.repaint();
                AdbHudDevice adbHudDevice = (AdbHudDevice) getDevice();
                OperationResult operationResult = adbHudDevice.screenshot();
                if (operationResult.isOk()) {
                    String filePath = (String) operationResult.getData();
                    try {
                        BufferedImage bufferedImage = ImageIO.read(new File(filePath));
                        getPicturePanel().setImageStream(bufferedImage);
                    } catch (IOException e) {
                        SwingUtil.showWarningDialog(AdbHudPictureContainer.this, e.getMessage());
                    }
                } else {
                    SwingUtil.showWarningDialog(AdbHudPictureContainer.this, operationResult.getMessage());
                }
                return null;
            }

            @Override
            protected void done() {
                screenShotButton.setEnabled(true);
                screenShotButton.repaint();
            }
        };
    }

    @Override
    public void createActions() {
        super.createActions();
        screenShotButton.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                if (screenShotButton.isEnabled()) {
                    screenShot();
                }
            }
        });
    }

    @Override
    public boolean pictureOperationStart(OperationMethod operationMethod) {
        return true;
    }

    @Override
    public boolean pictureOperating(Operation operation) {
        return true;
    }

    @Override
    public void pictureDoubleClick(ScaledPoint point) {
    }

    public void grab() {
        AdbHudDevice device = (AdbHudDevice) getDevice();
        String deviceName = device.getDeviceName();

        // 如果已经有视频流在运行，先停止
        if (isStreamRunning) {
            stopVideoStream();
        }

        log.info("开始启动ADB HUD设备视频流: {}", deviceName);

        videoStreamWorker = new SwingWorker<Boolean, BufferedImage>() {
            @Override
            protected Boolean doInBackground() throws Exception {
                try {
                    // 确保设备已连接
                    if (!device.isConnected()) {
                        OperationResult response = device.openDevice();
                        if (!response.isOk()) {
                            getPicturePanel().setText("设备连接失败: " + response.getMessage());
                            return false;
                        }
                    }

                    startGrab();
                    publish((BufferedImage) null); // 清空显示
                    getPicturePanel().setText("正在连接ADB HUD设备视频流: " + deviceName);

                    // 调用设备的videoStream方法，这会直接调用后端的/android/screencap接口
                    OperationResult result = device.videoStream();
                    if (result != null && result.isOk()) {
                        // 从结果中获取VideoStreamHandler
                        Object data = result.getData();
                        if (data instanceof VideoStreamHandler) {
                            videoStreamHandler = (VideoStreamHandler) data;
                        } else {
                            log.warn("videoStream返回的数据类型不正确: {}", data != null ? data.getClass() : "null");
                            getPicturePanel().setText("视频流启动失败: 返回数据类型错误");
                            return false;
                        }
                    } else {
                        String errorMsg = result != null ? result.getMessage() : "未知错误";
                        log.error("设备videoStream方法调用失败: {}", errorMsg);
                        getPicturePanel().setText("无法启动视频流: " + errorMsg);
                        return false;
                    }

                    isStreamRunning = true;
                    getPicturePanel().clearText();
                    completeGrab();

                    // 持续获取视频帧
                    while (isStreamRunning && !isCancelled() && videoStreamHandler.isRunning()) {
                        BufferedImage frame = videoStreamHandler.getNextFrame();
                        if (frame != null) {
                            publish(frame);
                        } else {
                            // 没有新帧，短暂休眠避免CPU占用过高
                            Thread.sleep(16); // 约60fps
                        }
                    }

                    return true;
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.info("ADB HUD视频流获取被中断: {}", deviceName);
                    return false;
                } catch (Exception e) {
                    log.error("ADB HUD视频流获取失败: {}, 错误: {}", deviceName, e.getMessage(), e);
                    getPicturePanel().setText("视频流获取失败: " + e.getMessage());
                    return false;
                }
            }

            @Override
            protected void process(List<BufferedImage> chunks) {
                if (!chunks.isEmpty() && getPicturePanel() != null) {
                    BufferedImage latestFrame = chunks.get(chunks.size() - 1);
                    if (latestFrame != null) {
                        getPicturePanel().setImageStream(latestFrame);
                    }
                }
            }

            @Override
            protected void done() {
                try {
                    Boolean result = get();
                    if (!result) {
                        log.warn("ADB HUD视频流获取任务完成，但结果为失败");
                    }
                } catch (Exception e) {
                    log.error("ADB HUD视频流获取任务异常", e);
                } finally {
                    stopVideoStream();
                }
            }
        };

        videoStreamWorker.execute();
    }

    /**
     * 停止视频流
     */
    private void stopVideoStream() {
        isStreamRunning = false;

        // 停止视频流处理器
        if (videoStreamHandler != null) {
            String deviceName = getDevice().getDeviceName();
            VideoStreamClient.stopVideoStream(deviceName);
            videoStreamHandler = null;
        }

        // 取消工作线程
        if (videoStreamWorker != null && !videoStreamWorker.isDone()) {
            videoStreamWorker.cancel(true);
        }

        log.info("ADB HUD视频流已停止: {}", getDevice().getDeviceName());
    }

    @Override
    public void appExit() {
        stopVideoStream();
    }

}
