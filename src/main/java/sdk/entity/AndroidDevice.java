package sdk.entity;

import com.alibaba.fastjson2.TypeReference;
import common.constant.DeviceCategoryConstants;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import sdk.base.BaseHttpClient;
import sdk.base.JsonResponse;
import sdk.base.operation.OperationResult;
import sdk.constants.DeviceModel;
import sdk.constants.DeviceType;
import sdk.constants.UrlConstants;
import sdk.constants.methods.DeviceMethods;
import sdk.domain.Device;
import sdk.entity.interfaces.IAndroid;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * Android设备
 */
@Slf4j
public class AndroidDevice extends DefaultVisionDevice implements IAndroid {

    public AndroidDevice() {
        super(DeviceModel.Android.USB_ANDROID);
    }

    @Override
    public String getDeviceTypeName() {
        return DeviceCategoryConstants.ANDROID.getDeviceOfficialName();
    }

    @Override
    public String getDeviceType() {
        return DeviceType.DEVICE_ANDROID;
    }

    @Override
    public JsonResponse<Device> registerDevice(Device device) {
        return registerDevice(device, new TypeReference<JsonResponse<AndroidDevice>>() {
        });
    }

    @Override
    public JsonResponse<Device> registerAndOpenDevice(Device device) {
        return registerAndOpenDevice(device, new TypeReference<JsonResponse<AndroidDevice>>() {
        });
    }

    @Override
    public JsonResponse<List<? extends Device>> queryDevices() {
        JsonResponse<List<AndroidDevice>> resp = defaultGetJsonResponse(UrlConstants.DeviceUrls.PhysicalPortUrls.getAllAndroids(getDeviceModel()),
                new TypeReference<JsonResponse<List<AndroidDevice>>>() {
                });
        return JsonResponse.retrieve(resp);
    }

    @Override
    public OperationResult screenshot() {
        return callOperationMethod(DeviceMethods.screenshot);
    }

    @Override
    public InputStream videoStream() {
        return videoStream(0, 0, 0);
    }

    @Override
    public InputStream videoStream(int width, int height, int bitRate) {
        try {
            // 直接调用后端的/android/screencap接口获取视频流
            String deviceName = getDeviceName();
            if (deviceName == null || deviceName.trim().isEmpty()) {
                log.error("设备名称为空，无法启动视频流");
                return null;
            }

            // 构建请求URL
            String url;
            if (width == 0 && height == 0 && bitRate == 0) {
                url = UrlConstants.AndroidScreencapUrls.getVideoStreamUrl(deviceName);
            } else {
                url = UrlConstants.AndroidScreencapUrls.getVideoStreamUrlWithParams(deviceName, width, height, bitRate);
            }

            log.info("请求Android设备视频流: 设备={}, URL={}", deviceName, url);

            // 直接返回HTTP响应的InputStream
            InputStream inputStream = getVideoStreamInputStream(url);
            if (inputStream != null) {
                log.info("Android设备视频流启动成功: 设备={}, 分辨率={}x{}, 比特率={}",
                    deviceName, width, height, bitRate);
            } else {
                log.error("Android设备视频流启动失败: 设备={}", deviceName);
            }
            return inputStream;

        } catch (Exception e) {
            log.error("Android设备视频流启动异常: 设备={}, 错误={}", getDeviceName(), e.getMessage(), e);
            return null;
        }
    }

    /**
     * 获取视频流的InputStream
     * @param url 视频流URL
     * @return InputStream，失败时返回null
     */
    private InputStream getVideoStreamInputStream(String url) {
        try {
            // 创建专用于视频流的HTTP客户端（无超时限制）
            OkHttpClient streamHttpClient = new OkHttpClient.Builder()
                    .connectTimeout(30, TimeUnit.SECONDS)
                    .readTimeout(0, TimeUnit.SECONDS)  // 无读取超时
                    .writeTimeout(0, TimeUnit.SECONDS) // 无写入超时
                    .build();

            // 构建请求
            Request request = new Request.Builder()
                    .url(url)
                    .get()
                    .headers(BaseHttpClient.formHeaders())
                    .build();

            // 发送请求
            Response response = streamHttpClient.newCall(request).execute();

            if (!response.isSuccessful()) {
                log.error("请求视频流失败: 状态码={}, 消息={}", response.code(), response.message());
                response.close();
                return null;
            }

            if (response.body() == null) {
                log.error("视频流响应体为空");
                response.close();
                return null;
            }

            // 返回响应体的InputStream
            return response.body().byteStream();

        } catch (IOException e) {
            log.error("获取视频流InputStream失败: {}", e.getMessage(), e);
            return null;
        }
    }

    public OperationResult executeAdbCommand(String command) {
        return callOperationMethod(DeviceMethods.executeADBCommand, command);
    }
}
