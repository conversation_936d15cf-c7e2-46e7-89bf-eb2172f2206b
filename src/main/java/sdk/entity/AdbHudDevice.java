package sdk.entity;

import com.alibaba.fastjson2.TypeReference;
import common.constant.DeviceCategoryConstants;
import sdk.base.JsonResponse;
import sdk.base.operation.OperationResult;
import sdk.constants.DeviceModel;
import sdk.constants.DeviceType;
import sdk.constants.UrlConstants;
import sdk.constants.methods.DeviceMethods;
import sdk.domain.Device;
import sdk.domain.VideoStreamParams;
import sdk.entity.interfaces.IAndroid;
import sdk.utils.VideoStreamClient;
import ui.base.picture.AdbMessages;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import sdk.base.BaseHttpClient;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * ADBHUD设备
 */
@Slf4j
public class AdbHudDevice extends DefaultVisionDevice implements IAndroid {

    public AdbHudDevice() {
        super(DeviceModel.Android.ADB_HUD);
    }

    @Override
    public String getDeviceTypeName() {
        return DeviceCategoryConstants.ANDROID.getDeviceOfficialName();
    }

    @Override
    public String getDeviceType() {
        return DeviceType.DEVICE_ANDROID;
    }

    @Override
    public JsonResponse<Device> registerDevice(Device device) {
        return registerDevice(device, new TypeReference<JsonResponse<AdbHudDevice>>() {
        });
    }

    @Override
    public JsonResponse<Device> registerAndOpenDevice(Device device) {
        return registerAndOpenDevice(device, new TypeReference<JsonResponse<AdbHudDevice>>() {
        });
    }

    @Override
    public JsonResponse<List<? extends Device>> queryDevices() {
        JsonResponse<List<AdbHudDevice>> resp = defaultGetJsonResponse(UrlConstants.DeviceUrls.PhysicalPortUrls.getAllAndroids(getDeviceModel()),
                new TypeReference<JsonResponse<List<AdbHudDevice>>>() {
                });
        return JsonResponse.retrieve(resp);
    }

    @Override
    public OperationResult screenshot() {
        return callOperationMethod(DeviceMethods.screenshot);
    }

    @Override
    public OperationResult videoStream() {
        return videoStream(0, 0, 0);
    }

    @Override
    public OperationResult videoStream(int width, int height, int bitRate) {
        OperationResult result = new OperationResult();
        try {
            // 使用VideoStreamClient启动视频流，这会调用后端的/android/screencap接口
            String deviceName = getDeviceName();
            if (deviceName == null || deviceName.trim().isEmpty()) {
                return result.fail("设备名称为空");
            }

            // 启动视频流
            var streamHandler = VideoStreamClient.startVideoStream(deviceName, width, height, bitRate);
            if (streamHandler != null) {
                result.setOk(true);
                result.setMessage("视频流启动成功");
                result.setData(streamHandler);
                log.info("ADB HUD设备视频流启动成功: 设备={}, 分辨率={}x{}, 比特率={}",
                    deviceName, width, height, bitRate);
            } else {
                result.fail("无法启动视频流，请检查设备连接和后端服务");
                log.error("ADB HUD设备视频流启动失败: 设备={}", deviceName);
            }
        } catch (Exception e) {
            result.fail("启动视频流时发生异常: " + e.getMessage());
            log.error("ADB HUD设备视频流启动异常: 设备={}, 错误={}", getDeviceName(), e.getMessage(), e);
        }
        return result;
    }

    public OperationResult executeAdbCommand(String command) {
        return callOperationMethod(DeviceMethods.executeADBCommand, command);
    }

    public OperationResult executeAdbCommand(AdbMessages adbMessages) {
        return callOperationMethod(DeviceMethods.executeADBCommand, adbMessages);
    }
}
