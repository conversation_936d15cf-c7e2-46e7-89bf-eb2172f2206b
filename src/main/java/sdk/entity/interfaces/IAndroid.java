package sdk.entity.interfaces;

import sdk.base.operation.OperationResult;

import java.io.ByteArrayInputStream;
import java.io.InputStream;

public interface IAndroid {

    OperationResult screenshot();

    default InputStream videoStream() {
        return new ByteArrayInputStream(new byte[0]);
    }

    default InputStream videoStream(int width, int height, int bitRate) {
        return videoStream();
    }


}
