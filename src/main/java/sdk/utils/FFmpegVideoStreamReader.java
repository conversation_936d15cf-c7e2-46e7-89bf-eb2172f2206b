package sdk.utils;

import lombok.extern.slf4j.Slf4j;
import org.bytedeco.javacv.FFmpegFrameGrabber;
import org.bytedeco.javacv.Frame;
import org.bytedeco.javacv.Java2DFrameConverter;

import java.awt.image.BufferedImage;
import java.io.InputStream;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 基于FFmpeg的高性能视频流读取器
 * 专门优化用于处理2K分辨率的Android设备视频流
 */
@Slf4j
public class FFmpegVideoStreamReader {
    
    private final InputStream inputStream;
    private final BlockingQueue<BufferedImage> frameQueue;
    private final AtomicBoolean isRunning;
    private final AtomicBoolean isReading;
    
    private FFmpegFrameGrabber grabber;
    private Java2DFrameConverter converter;
    private Thread readerThread;
    
    // 性能统计
    private long frameCount = 0;
    private long lastStatsTime = System.currentTimeMillis();
    private long totalBytesRead = 0;
    
    // 配置参数
    private static final int FRAME_QUEUE_SIZE = 30; // 帧队列大小
    private static final int STATS_INTERVAL = 5000; // 统计间隔(ms)
    
    public FFmpegVideoStreamReader(InputStream inputStream) {
        this.inputStream = inputStream;
        this.frameQueue = new LinkedBlockingQueue<>(FRAME_QUEUE_SIZE);
        this.isRunning = new AtomicBoolean(false);
        this.isReading = new AtomicBoolean(false);
        this.converter = new Java2DFrameConverter();
    }
    
    /**
     * 启动视频流读取
     */
    public boolean start() {
        if (isRunning.get()) {
            log.warn("FFmpeg视频流读取器已经在运行");
            return false;
        }
        
        try {
            // 创建FFmpeg帧抓取器
            grabber = new FFmpegFrameGrabber(inputStream);
            
            // 优化配置
            grabber.setOption("probesize", "32768");     // 减少探测大小，加快启动
            grabber.setOption("analyzeduration", "1000000"); // 1秒分析时间
            grabber.setOption("fflags", "nobuffer");     // 禁用缓冲，减少延迟
            grabber.setOption("flags", "low_delay");     // 低延迟模式
            grabber.setOption("strict", "experimental"); // 允许实验性编解码器
            
            // 针对2K分辨率的优化
            grabber.setOption("threads", "auto");        // 自动线程数
            grabber.setOption("thread_type", "slice");   // 切片线程类型
            
            // 启动抓取器
            grabber.start();
            
            isRunning.set(true);
            isReading.set(true);
            
            // 启动读取线程
            readerThread = new Thread(this::readFrames, "FFmpeg-VideoStream-Reader");
            readerThread.setDaemon(true);
            readerThread.start();
            
            log.info("FFmpeg视频流读取器启动成功");
            return true;
            
        } catch (Exception e) {
            log.error("启动FFmpeg视频流读取器失败: {}", e.getMessage(), e);
            cleanup();
            return false;
        }
    }
    
    /**
     * 获取下一帧（非阻塞）
     */
    public BufferedImage getNextFrame() {
        return frameQueue.poll();
    }
    
    /**
     * 获取下一帧（阻塞，带超时）
     */
    public BufferedImage getNextFrame(long timeoutMs) {
        try {
            return frameQueue.poll(timeoutMs, java.util.concurrent.TimeUnit.MILLISECONDS);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            return null;
        }
    }
    
    /**
     * 检查是否正在运行
     */
    public boolean isRunning() {
        return isRunning.get();
    }
    
    /**
     * 获取队列中的帧数量
     */
    public int getQueueSize() {
        return frameQueue.size();
    }
    
    /**
     * 获取帧率统计
     */
    public double getCurrentFPS() {
        long currentTime = System.currentTimeMillis();
        long timeDiff = currentTime - lastStatsTime;
        if (timeDiff > 0) {
            return (frameCount * 1000.0) / timeDiff;
        }
        return 0.0;
    }
    
    /**
     * 停止视频流读取
     */
    public void stop() {
        if (!isRunning.get()) {
            return;
        }
        
        log.info("正在停止FFmpeg视频流读取器...");
        isRunning.set(false);
        isReading.set(false);
        
        // 等待读取线程结束
        if (readerThread != null && readerThread.isAlive()) {
            try {
                readerThread.interrupt();
                readerThread.join(3000); // 最多等待3秒
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
        
        cleanup();
        log.info("FFmpeg视频流读取器已停止");
    }
    
    /**
     * 读取帧的主循环
     */
    private void readFrames() {
        log.info("FFmpeg帧读取线程开始");
        
        try {
            while (isReading.get() && !Thread.currentThread().isInterrupted()) {
                try {
                    // 从FFmpeg抓取帧
                    Frame frame = grabber.grab();
                    if (frame == null) {
                        log.info("视频流结束");
                        break;
                    }
                    
                    // 只处理图像帧
                    if (frame.image != null) {
                        // 转换为BufferedImage
                        BufferedImage bufferedImage = converter.convert(frame);
                        if (bufferedImage != null) {
                            // 如果队列满了，移除最老的帧
                            if (frameQueue.remainingCapacity() == 0) {
                                frameQueue.poll(); // 移除最老的帧
                            }
                            
                            // 添加新帧
                            if (frameQueue.offer(bufferedImage)) {
                                frameCount++;
                                logStats();
                            }
                        }
                    }
                    
                } catch (Exception e) {
                    if (isReading.get()) {
                        log.error("读取视频帧失败: {}", e.getMessage());
                        // 短暂休眠后继续尝试
                        Thread.sleep(10);
                    }
                }
            }
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.info("FFmpeg帧读取线程被中断");
        } catch (Exception e) {
            log.error("FFmpeg帧读取线程异常: {}", e.getMessage(), e);
        } finally {
            log.info("FFmpeg帧读取线程结束");
        }
    }
    
    /**
     * 记录性能统计
     */
    private void logStats() {
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastStatsTime >= STATS_INTERVAL) {
            double fps = getCurrentFPS();
            int queueSize = getQueueSize();
            
            log.info("FFmpeg视频流统计: {:.1f} fps, 队列大小: {}, 总帧数: {}", 
                fps, queueSize, frameCount);
            
            // 重置统计
            frameCount = 0;
            lastStatsTime = currentTime;
        }
    }
    
    /**
     * 清理资源
     */
    private void cleanup() {
        try {
            if (grabber != null) {
                grabber.stop();
                grabber.release();
                grabber = null;
            }
            
            if (converter != null) {
                converter.close();
                converter = null;
            }
            
            frameQueue.clear();
            
        } catch (Exception e) {
            log.warn("清理FFmpeg资源时出错: {}", e.getMessage());
        }
    }
}
