package sdk.utils;

import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import sdk.base.BaseHttpClient;
import sdk.base.VideoStreamHandler;
import sdk.constants.UrlConstants;

import java.awt.image.BufferedImage;
import java.io.IOException;
import java.io.InputStream;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 视频流客户端
 * 负责请求和管理Android设备的视频流
 */
@Slf4j
public class VideoStreamClient {
    
    private static final int STREAM_TIMEOUT = 0; // 无限超时，适用于长连接流
    private static final OkHttpClient streamHttpClient = new OkHttpClient.Builder()
            .connectTimeout(30, TimeUnit.SECONDS)
            .readTimeout(STREAM_TIMEOUT, TimeUnit.SECONDS)
            .writeTimeout(STREAM_TIMEOUT, TimeUnit.SECONDS)
            .build();
    
    // 管理活跃的视频流连接
    private static final ConcurrentHashMap<String, VideoStreamConnection> activeConnections = new ConcurrentHashMap<>();
    
    /**
     * 视频流连接信息
     */
    private static class VideoStreamConnection {
        private final Response response;
        private final VideoStreamHandler handler;
        private final AtomicBoolean isActive;
        
        public VideoStreamConnection(Response response, VideoStreamHandler handler) {
            this.response = response;
            this.handler = handler;
            this.isActive = new AtomicBoolean(true);
        }
        
        public void close() {
            if (isActive.compareAndSet(true, false)) {
                handler.stopProcessing();
                if (response != null) {
                    response.close();
                }
            }
        }
        
        public VideoStreamHandler getHandler() {
            return handler;
        }
        
        public boolean isActive() {
            return isActive.get();
        }
    }
    
    /**
     * 开始Android设备视频流（默认参数）
     * @param deviceName 设备名称
     * @return 视频流处理器，如果失败返回null
     */
    public static VideoStreamHandler startVideoStream(String deviceName) {
        return startVideoStreamWithParams(deviceName, 0, 0, 0);
    }

    /**
     * 开始Android设备视频流（带参数）
     * @param deviceName 设备名称
     * @param width 视频宽度（0表示使用默认值）
     * @param height 视频高度（0表示使用默认值）
     * @param bitRate 视频比特率（0表示使用默认值）
     * @return 视频流处理器，如果失败返回null
     */
    public static VideoStreamHandler startVideoStream(String deviceName, int width, int height, int bitRate) {
        return startVideoStreamWithParams(deviceName, width, height, bitRate);
    }
    
    /**
     * 开始Android设备视频流（带参数）
     * @param deviceName 设备名称
     * @param width 视频宽度（0表示使用默认值）
     * @param height 视频高度（0表示使用默认值）
     * @param bitRate 视频比特率（0表示使用默认值）
     * @return 视频流处理器，如果失败返回null
     */
    public static VideoStreamHandler startVideoStreamWithParams(String deviceName, int width, int height, int bitRate) {
        // 检查是否已有活跃连接
        if (hasActiveStream(deviceName)) {
            log.warn("设备 {} 已有活跃的视频流连接", deviceName);
            return getActiveStreamHandler(deviceName);
        }
        
        try {
            // 构建请求URL
            String url;
            if (width == 0 && height == 0 && bitRate == 0) {
                url = UrlConstants.AndroidScreencapUrls.getVideoStreamUrl(deviceName);
            } else {
                url = UrlConstants.AndroidScreencapUrls.getVideoStreamUrlWithParams(deviceName, width, height, bitRate);
            }
            
            log.info("请求Android设备视频流: 设备={}, URL={}", deviceName, url);
            
            // 构建请求
            Request request = new Request.Builder()
                    .url(url)
                    .get()
                    .headers(BaseHttpClient.formHeaders())
                    .build();
            
            // 发送请求
            Response response = streamHttpClient.newCall(request).execute();
            
            if (!response.isSuccessful()) {
                log.error("请求视频流失败: 设备={}, 状态码={}, 消息={}", 
                    deviceName, response.code(), response.message());
                response.close();
                return null;
            }
            
            if (response.body() == null) {
                log.error("视频流响应体为空: 设备={}", deviceName);
                response.close();
                return null;
            }
            
            // 创建视频流处理器
            VideoStreamHandler handler = new VideoStreamHandler(deviceName);
            InputStream inputStream = response.body().byteStream();
            
            // 开始处理视频流
            handler.startProcessing(inputStream);
            
            // 保存连接信息
            VideoStreamConnection connection = new VideoStreamConnection(response, handler);
            activeConnections.put(deviceName, connection);
            
            log.info("成功启动Android设备视频流: 设备={}", deviceName);
            return handler;
            
        } catch (IOException e) {
            log.error("启动Android设备视频流失败: 设备={}, 错误={}", deviceName, e.getMessage(), e);
            return null;
        } catch (Exception e) {
            log.error("启动Android设备视频流时发生未知错误: 设备={}, 错误={}", deviceName, e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * 停止指定设备的视频流
     * @param deviceName 设备名称
     */
    public static void stopVideoStream(String deviceName) {
        VideoStreamConnection connection = activeConnections.remove(deviceName);
        if (connection != null) {
            connection.close();
            log.info("已停止Android设备视频流: {}", deviceName);
        } else {
            log.warn("未找到设备 {} 的活跃视频流连接", deviceName);
        }
    }
    
    /**
     * 检查指定设备是否有活跃的视频流
     * @param deviceName 设备名称
     * @return 如果有活跃流返回true
     */
    public static boolean hasActiveStream(String deviceName) {
        VideoStreamConnection connection = activeConnections.get(deviceName);
        return connection != null && connection.isActive();
    }
    
    /**
     * 获取指定设备的活跃视频流处理器
     * @param deviceName 设备名称
     * @return 视频流处理器，如果没有活跃连接返回null
     */
    public static VideoStreamHandler getActiveStreamHandler(String deviceName) {
        VideoStreamConnection connection = activeConnections.get(deviceName);
        if (connection != null && connection.isActive()) {
            return connection.getHandler();
        }
        return null;
    }
    
    /**
     * 获取下一帧图像（非阻塞）
     * @param deviceName 设备名称
     * @return 图像帧，如果没有可用帧或没有活跃连接则返回null
     */
    public static BufferedImage getNextFrame(String deviceName) {
        VideoStreamHandler handler = getActiveStreamHandler(deviceName);
        if (handler != null) {
            return handler.getNextFrame();
        }
        return null;
    }
    
    /**
     * 获取当前活跃的视频流数量
     * @return 活跃视频流数量
     */
    public static int getActiveStreamCount() {
        return (int) activeConnections.values().stream()
                .filter(VideoStreamConnection::isActive)
                .count();
    }
    
    /**
     * 停止所有活跃的视频流
     */
    public static void stopAllStreams() {
        log.info("停止所有活跃的Android设备视频流，当前数量: {}", activeConnections.size());
        
        activeConnections.forEach((deviceName, connection) -> {
            connection.close();
            log.info("已停止设备视频流: {}", deviceName);
        });
        
        activeConnections.clear();
    }
    
    /**
     * 清理无效的连接
     */
    public static void cleanupInactiveConnections() {
        activeConnections.entrySet().removeIf(entry -> {
            if (!entry.getValue().isActive()) {
                log.debug("清理无效连接: {}", entry.getKey());
                return true;
            }
            return false;
        });
    }
}
