package sdk.utils;

import lombok.extern.slf4j.Slf4j;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;

/**
 * 视频流优化工具类
 * 提供高效的视频流读取和帧解析功能
 */
@Slf4j
public class VideoStreamOptimizer {
    
    // JPEG文件头标识
    private static final byte[] JPEG_HEADER = {(byte) 0xFF, (byte) 0xD8};
    // JPEG文件尾标识
    private static final byte[] JPEG_FOOTER = {(byte) 0xFF, (byte) 0xD9};
    
    /**
     * 从输入流中读取完整的JPEG帧
     * @param inputStream 输入流
     * @param buffer 读取缓冲区
     * @param frameBuffer 帧数据缓冲区
     * @return 完整的JPEG帧数据，如果没有完整帧则返回null
     */
    public static byte[] readCompleteJpegFrame(InputStream inputStream, byte[] buffer, ByteArrayOutputStream frameBuffer) {
        try {
            int bytesRead = inputStream.read(buffer);
            if (bytesRead <= 0) {
                return null;
            }
            
            frameBuffer.write(buffer, 0, bytesRead);
            byte[] data = frameBuffer.toByteArray();
            
            // 查找JPEG帧的开始和结束
            int startIndex = findJpegStart(data);
            if (startIndex == -1) {
                // 没有找到JPEG开始标识，继续累积数据
                return null;
            }
            
            int endIndex = findJpegEnd(data, startIndex);
            if (endIndex == -1) {
                // 没有找到JPEG结束标识，继续累积数据
                return null;
            }
            
            // 提取完整的JPEG帧
            int frameLength = endIndex - startIndex + 2; // +2 包含结束标识
            byte[] jpegFrame = new byte[frameLength];
            System.arraycopy(data, startIndex, jpegFrame, 0, frameLength);
            
            // 移除已处理的数据，保留剩余数据
            int remainingLength = data.length - endIndex - 2;
            if (remainingLength > 0) {
                byte[] remaining = new byte[remainingLength];
                System.arraycopy(data, endIndex + 2, remaining, 0, remainingLength);
                frameBuffer.reset();
                frameBuffer.write(remaining);
            } else {
                frameBuffer.reset();
            }
            
            return jpegFrame;
            
        } catch (IOException e) {
            log.error("读取JPEG帧失败: {}", e.getMessage());
            return null;
        }
    }
    
    /**
     * 查找JPEG开始标识
     */
    private static int findJpegStart(byte[] data) {
        for (int i = 0; i <= data.length - JPEG_HEADER.length; i++) {
            if (data[i] == JPEG_HEADER[0] && data[i + 1] == JPEG_HEADER[1]) {
                return i;
            }
        }
        return -1;
    }
    
    /**
     * 查找JPEG结束标识
     */
    private static int findJpegEnd(byte[] data, int startIndex) {
        for (int i = startIndex + JPEG_HEADER.length; i <= data.length - JPEG_FOOTER.length; i++) {
            if (data[i] == JPEG_FOOTER[0] && data[i + 1] == JPEG_FOOTER[1]) {
                return i;
            }
        }
        return -1;
    }
    
    /**
     * 解析JPEG数据为BufferedImage
     */
    public static BufferedImage parseJpegFrame(byte[] jpegData) {
        try (ByteArrayInputStream imageStream = new ByteArrayInputStream(jpegData)) {
            return ImageIO.read(imageStream);
        } catch (IOException e) {
            log.debug("解析JPEG帧失败: {}", e.getMessage());
            return null;
        }
    }
    
    /**
     * 2K分辨率优化的视频流读取器
     */
    public static class HighResolutionStreamReader {
        private final InputStream inputStream;
        private final ByteArrayOutputStream frameBuffer;
        private final byte[] readBuffer;
        private long frameCount = 0;
        private long lastStatsTime = System.currentTimeMillis();
        private long totalBytesRead = 0;

        public HighResolutionStreamReader(InputStream inputStream) {
            this.inputStream = new java.io.BufferedInputStream(inputStream, 1024 * 1024); // 1MB缓冲
            this.frameBuffer = new ByteArrayOutputStream(3 * 1024 * 1024); // 预分配3MB，适合2K图像
            this.readBuffer = new byte[512 * 1024]; // 512KB读取缓冲区
        }

        /**
         * 读取下一帧（2K优化版本）
         * @return BufferedImage或null
         */
        public BufferedImage readNextFrame() {
            try {
                int bytesRead = inputStream.read(readBuffer);
                if (bytesRead <= 0) {
                    return null;
                }

                totalBytesRead += bytesRead;
                frameBuffer.write(readBuffer, 0, bytesRead);

                // 2K图像需要更大的数据量才尝试解析
                if (frameBuffer.size() > 100 * 1024) { // 至少100KB
                    byte[] jpegData = readCompleteJpegFrame(inputStream, readBuffer, frameBuffer);
                    if (jpegData != null && jpegData.length > 50 * 1024) { // 2K JPEG至少50KB
                        BufferedImage frame = parseJpegFrame(jpegData);
                        if (frame != null) {
                            frameCount++;
                            logStats();
                            return frame;
                        }
                    }
                }

                // 防止缓冲区过大
                if (frameBuffer.size() > 8 * 1024 * 1024) { // 8MB限制
                    log.warn("2K视频流缓冲区过大，重置: {} bytes", frameBuffer.size());
                    frameBuffer.reset();
                }

            } catch (Exception e) {
                log.error("读取2K视频流帧失败: {}", e.getMessage());
            }

            return null;
        }
    }

    /**
     * 标准优化的视频流读取器
     */
    public static class OptimizedStreamReader {
        private final InputStream inputStream;
        private final ByteArrayOutputStream frameBuffer;
        private final byte[] readBuffer;
        private long frameCount = 0;
        private long lastStatsTime = System.currentTimeMillis();

        public OptimizedStreamReader(InputStream inputStream, int bufferSize) {
            this.inputStream = inputStream;
            this.frameBuffer = new ByteArrayOutputStream();
            this.readBuffer = new byte[bufferSize];
        }
        
        /**
         * 读取下一帧
         * @return BufferedImage或null
         */
        public BufferedImage readNextFrame() {
            byte[] jpegData = readCompleteJpegFrame(inputStream, readBuffer, frameBuffer);
            if (jpegData != null) {
                BufferedImage frame = parseJpegFrame(jpegData);
                if (frame != null) {
                    frameCount++;
                    logStats();
                    return frame;
                }
            }
            return null;
        }
        
        private void logStats() {
            long currentTime = System.currentTimeMillis();
            if (currentTime - lastStatsTime >= 5000) { // 每5秒统计一次
                double fps = frameCount / ((currentTime - lastStatsTime) / 1000.0);
                log.debug("视频流统计: {} 帧, {:.1f} fps", frameCount, fps);
                frameCount = 0;
                lastStatsTime = currentTime;
            }
        }
        
        /**
         * 关闭读取器
         */
        public void close() {
            try {
                if (inputStream != null) {
                    inputStream.close();
                }
                if (frameBuffer != null) {
                    frameBuffer.close();
                }
            } catch (IOException e) {
                log.warn("关闭视频流读取器失败: {}", e.getMessage());
            }
        }
    }
}
