package sdk.base;

import lombok.extern.slf4j.Slf4j;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 视频流处理器
 * 负责处理从后端接收的视频流数据，将字节流转换为图像帧
 */
@Slf4j
public class VideoStreamHandler {
    
    private final BlockingQueue<BufferedImage> frameQueue;
    private final AtomicBoolean isRunning;
    private final String deviceName;
    private Thread processingThread;
    
    public VideoStreamHandler(String deviceName) {
        this.deviceName = deviceName;
        this.frameQueue = new LinkedBlockingQueue<>(10); // 限制队列大小防止内存溢出
        this.isRunning = new AtomicBoolean(false);
    }
    
    /**
     * 开始处理视频流
     * @param inputStream 视频流输入流
     */
    public void startProcessing(InputStream inputStream) {
        if (isRunning.get()) {
            log.warn("视频流处理器已在运行中: {}", deviceName);
            return;
        }
        
        isRunning.set(true);
        processingThread = new Thread(() -> processVideoStream(inputStream), 
            "VideoStreamHandler-" + deviceName);
        processingThread.setDaemon(true);
        processingThread.start();
        
        log.info("开始处理视频流: {}", deviceName);
    }
    
    /**
     * 停止处理视频流
     */
    public void stopProcessing() {
        if (!isRunning.get()) {
            return;
        }
        
        isRunning.set(false);
        
        if (processingThread != null && processingThread.isAlive()) {
            processingThread.interrupt();
            try {
                processingThread.join(3000); // 等待最多3秒
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.warn("等待视频流处理线程结束时被中断: {}", deviceName);
            }
        }
        
        // 清空队列
        frameQueue.clear();
        
        log.info("停止处理视频流: {}", deviceName);
    }
    
    /**
     * 获取下一帧图像（非阻塞）
     * @return 图像帧，如果没有可用帧则返回null
     */
    public BufferedImage getNextFrame() {
        return frameQueue.poll();
    }
    
    /**
     * 获取下一帧图像（阻塞）
     * @return 图像帧
     * @throws InterruptedException 如果等待被中断
     */
    public BufferedImage takeNextFrame() throws InterruptedException {
        return frameQueue.take();
    }
    
    /**
     * 检查是否正在运行
     * @return 如果正在运行返回true
     */
    public boolean isRunning() {
        return isRunning.get();
    }
    
    /**
     * 获取当前队列中的帧数量
     * @return 帧数量
     */
    public int getQueueSize() {
        return frameQueue.size();
    }
    
    /**
     * 处理视频流的核心方法
     * @param inputStream 输入流
     */
    private void processVideoStream(InputStream inputStream) {
        byte[] buffer = new byte[8192];
        ByteArrayInputStream frameBuffer = null;
        
        try {
            while (isRunning.get() && !Thread.currentThread().isInterrupted()) {
                int bytesRead = inputStream.read(buffer);
                if (bytesRead == -1) {
                    log.info("视频流结束: {}", deviceName);
                    break;
                }
                
                if (bytesRead > 0) {
                    // 尝试解析图像帧
                    try {
                        frameBuffer = new ByteArrayInputStream(buffer, 0, bytesRead);
                        BufferedImage frame = ImageIO.read(frameBuffer);
                        
                        if (frame != null) {
                            // 如果队列满了，移除最旧的帧
                            if (frameQueue.remainingCapacity() == 0) {
                                frameQueue.poll();
                            }
                            
                            if (!frameQueue.offer(frame)) {
                                log.debug("无法添加帧到队列，队列可能已满: {}", deviceName);
                            }
                        }
                    } catch (IOException e) {
                        // 这可能是因为接收到的数据不是完整的图像帧
                        // 在实际的视频流中，我们可能需要更复杂的帧分割逻辑
                        log.debug("解析图像帧失败: {}, 错误: {}", deviceName, e.getMessage());
                    } finally {
                        if (frameBuffer != null) {
                            try {
                                frameBuffer.close();
                            } catch (IOException e) {
                                log.debug("关闭帧缓冲区失败: {}", e.getMessage());
                            }
                        }
                    }
                }
            }
        } catch (IOException e) {
            if (isRunning.get()) {
                log.error("处理视频流时发生IO异常: {}, 错误: {}", deviceName, e.getMessage());
            }
        } catch (Exception e) {
            log.error("处理视频流时发生未知异常: {}, 错误: {}", deviceName, e.getMessage(), e);
        } finally {
            try {
                inputStream.close();
            } catch (IOException e) {
                log.warn("关闭视频流输入流失败: {}, 错误: {}", deviceName, e.getMessage());
            }
        }
    }
}
